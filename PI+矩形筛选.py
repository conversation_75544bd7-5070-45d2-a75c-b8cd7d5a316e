import time,os,urandom,sys,gc#,image#如果要显示照片，则引用image模块处理图片信息 | 导入必要的模块：时间、操作系统、串口、系统、垃圾回收
from media.display import *
from media.media import *#显示图像需要用到Media的display和media模块
from media.sensor import *
from machine import FPIOA
from machine import Pin
from machine import UART
from ybUtils.YbUart import YbUart
from threshold_setter import ThresholdSetter

##TODO:串口发二进制变量,矩形大小过滤.矩形数组四个角分类,解决blobs 0点画图
#后撤问题，矩形过滤左上角（-1，-1，-1，-1）
#阈值难调问题，矩形识别不到问题
#色块大小过滤
#初始位置归位删除
#corner拆开
#设置图像大小
#学习异常捕获
WIDTH = 320
HEIGHT = 240
roi = (640,300,640,480)  #感兴趣区域
binary = (0, 35)      #图像二值化参数
#颜色识别阈值
red = (0, 100, 31, 127, -128, 127)
red1 = (0, 100, 39, 127, -128, 127)

green = (35, 100, -128, -44, -128, 127)
green1 = (0, 100, -128, -29, -128, 127)
#占空比
DC_x = 0
DC_y = 0
#pid参数
PID_Kp = 0.000003# 比例系数
PID_Ki = -0.00000# 积分系数
PID_Kd = -0.000# 微分系数
PID_PARAMS = (PID_Kp, PID_Ki, PID_Kd)
P_GAIN = -7.0 # 新的比例增益，用于将像素误差转换为电机步数 (负号是因为摄像头和电机方向可能相反)
I_GAIN = -0.15 # 积分增益，用于消除稳态误差
DEAD_ZONE = 5  # 中心死区，误差小于这个值不移动
MAX_INTEGRAL = 1000  # 积分项最大值，防止积分饱和

# 矩形识别改进参数
MIN_RECT_AREA = 100      # 最小矩形面积
MAX_ASPECT_RATIO = 1.18   # 最大长宽比
MAX_AREA_RATIO = 0.3     # 最大面积占比
ENABLE_MORPHOLOGY = False # 是否启用形态学操作

#起始舵机位置，参数
MOVE_STEP_TOP = 80 #切段的函数
start_x = 0.500
start_y = 0.500
START_POSITION = (start_x, start_y)
laser_x = 0
laser_y = 0
#---------------------------------------PID类定义------------------------------------------------#

try:

    class PID:
        def __init__(self, kp, ki, kd, input_value, target):
            self.e = 0              # 当前误差
            self.e_last = 0         # 上一次误差
            self.e_sum = 0          # 误差累积，用于积分项
            self.kp = kp            # 比例系数
            self.ki = ki            # 积分系数
            self.kd = kd            # 微分系数
            self.target = target    # 目标值
            self.input_value = input_value  # PWM占空比
            self.max_sum = 200      # 积分项最大值，防止积分饱和

        def cal(self, value):       # value观测值，识别到激光点的坐标
            self.e = self.target - value  # 目标值 - 观测值 = 误差

            # 积分项累加
            self.e_sum += self.e
            # 积分限幅，防止积分饱和
            if self.e_sum > self.max_sum:
                self.e_sum = self.max_sum
            elif self.e_sum < -self.max_sum:
                self.e_sum = -self.max_sum

            # 标准PID算法实现
            p_out = self.kp * self.e                      # 比例项
            i_out = self.ki * self.e_sum                  # 积分项
            d_out = self.kd * (self.e - self.e_last)      # 微分项

            delta = p_out + i_out + d_out

            # 保存本次误差
            self.e_last = self.e

            # 更新PWM占空比
            self.input_value = self.input_value + delta
            return self.input_value


#---------------------------------------舵机移动函数定义------------------------------------------------#

    class LaserController:
        def __init__(self, pid_class, pid_params, start_position, move_step_top=120):
            # 初始化参数
            self.PID_Kp, self.PID_Ki, self.PID_Kd = pid_params
            self.start_x, self.start_y = start_position
            self.move_step_top = move_step_top
            self.PID = pid_class

            # 状态变量
            self.counter = 0
            self.move_pencil_step_n = 0
            self.servo_pid_x = None
            self.servo_pid_y = None
            self.servo_static_count = 0
            self.is_stable = False
            self.PID_isSet = 0

        def line_int(self, corners):
            """线性插值函数"""
            if self.counter == 4:  # 最后一段（连接到第一点）
                star_idx, tar_idx = 4, 1
            else:
                star_idx, tar_idx = self.counter, (self.counter + 1)
    #             计算当前目标位置
            target_x = (corners[tar_idx][0] - corners[star_idx][0]) / self.move_step_top * self.move_pencil_step_n + corners[star_idx][0]
            target_y = (corners[tar_idx][1] - corners[star_idx][1]) / self.move_step_top * self.move_pencil_step_n + corners[star_idx][1]
    #             更新步进计数
            if self.is_stable:
                if self.move_pencil_step_n == self.move_step_top:
                    self.counter = (self.counter + 1)
                    if self.counter == 5:
                       self.counter = 1
                    self.move_pencil_step_n = 0
                else:
                    self.move_pencil_step_n += 1

            return target_x, target_y


        def servo_move(self, target_x, target_y, laser_x, laser_y):
            if self.PID_isSet == 0:
                self.servo_pid_x = self.PID(self.PID_Kp, self.PID_Ki, self.PID_Kd, self.start_x, target_x)
                self.servo_pid_y = self.PID(-self.PID_Kp, self.PID_Ki, self.PID_Kd, self.start_y, target_y)
                self.PID_isSet = 1
            if self.servo_pid_x.target != target_x:
                self.servo_pid_x.target = target_x
                self.servo_pid_x.e_sum = 0  # 重置积分项
            if self.servo_pid_y.target != target_y:
                self.servo_pid_y.target = target_y
                self.servo_pid_y.e_sum = 0  # 重置积分项

            # 计算新的占空比
            duty = self.servo_pid_x.cal(laser_x)
            duty = max(0.5, min(2.5, duty))  # 限制在0.5-2.5ms范围
            new_duty_x = round(duty * 1000, 2)  # 转换为500-2500范围

            duty = self.servo_pid_y.cal(laser_y)
            duty = max(0.5, min(2.5, duty))  # 限制在0.5-2.5ms范围
            new_duty_y = round(duty * 1000, 2)  # 转换为500-2500范围
            # 检查位置稳定性
            if abs(target_x - laser_x) <= 7 and abs(target_y - laser_y) <= 7:
                self.servo_static_count += 1
            else:
                self.servo_static_count = 0

            self.is_stable = self.servo_static_count >= 3
            #返回计算好的x,y轴PWM
            return new_duty_x, new_duty_y

    #改进的矩形筛选函数
    def filter_and_select_rect(rects):
        """
        改进的矩形筛选函数，提高识别准确度
        """
        if not rects:
            return None

        valid_rects = []

        for rect in rects:
            w, h = rect.w(), rect.h()
            area = w * h

            # 1. 面积过滤：过滤太小的矩形（噪声）
            if area < MIN_RECT_AREA:
                continue

            # 2. 长宽比过滤：过滤过于细长的矩形
            aspect_ratio = max(w, h) / min(w, h)
            if aspect_ratio > MAX_ASPECT_RATIO:
                continue

            # 3. 尺寸过滤：过滤过大的矩形（可能是背景）
            if area > (WIDTH * HEIGHT * MAX_AREA_RATIO):
                continue

            # 4. 计算矩形质量分数（面积 + 形状因子）
            shape_factor = 1.0 / aspect_ratio  # 越接近正方形分数越高
            quality_score = area * shape_factor

            valid_rects.append((rect, quality_score))

        if not valid_rects:
            return None

        # 返回质量分数最高的矩形
        best_rect = max(valid_rects, key=lambda x: x[1])
        return best_rect[0]

#--------------------------------------串口对象初始化-------------------------------------------------#

    uart = YbUart(baudrate=115200)
    # laser_ctrl = LaserController(pid_class=PID, pid_params=PID_PARAMS, start_position=START_POSITION, move_step_top=MOVE_STEP_TOP)

    # PI控制器变量初始化
    error_integral = 0  # 积分项累积

#--------------------------------------FPIOA端口定义-------------------------------------------------#

    fpioa = FPIOA()#初始化FPIOA对象
    #设置按键
    fpioa.set_function(61, FPIOA.GPIO61)#确定该端口的功能为GPIO
    key = Pin(61, Pin.IN, Pin.PULL_DOWN)#设置fpioa61端口的模式，创建该端口对象

    #设置UART3_TXD
    fpioa.set_function(33, FPIOA.UART3_RXD, ie=1, oe=0)
    pin33 = Pin(33,Pin.OUT)#设置fpioa32端口的模式，创建该端口的对象
    #设置UART3_RXD
    fpioa.set_function(32, FPIOA.UART3_TXD, ie=0, oe=1)
    pin32 = Pin(32,Pin.OUT)#设置fpioa32端口的模式，创建该端口的对象

#    uart3 = UART(UART.UART3, 115200)
#    uart3.write('a%04d%04db' % (start_x * 100, start_y * 100))
    # uart.send('a%04d%04db' % (start_x * 1000, start_y * 1000))

#    aa = ThresholdSetter(red1, uart3)

#------------------------------------传感器配置初始化-----------------------------------------------#

    sensor = Sensor()   #初始化传感器变量
    sensor.reset()      #传感器重置
    sensor.set_framesize(width = WIDTH, height = HEIGHT, chn = CAM_CHN_ID_0)#设置通道0的输出尺寸
    sensor.set_pixformat(Sensor.RGB565,chn = CAM_CHN_ID_0)#设置通道0的输出格式为RGB565
    Display.init(Display.ST7701, width = 640, height = 480, to_ide = True)
    MediaManager.init()#初始化媒体管理器
    sensor.run()#开启传感器
    fps = time.clock()#创建时钟对象用于计算帧率


#---------------------------------------每一帧图像处理----------------------------------------------#

    while True:
        fps.tick()#帧率计时器tick
        os.exitpoint()#检查是否退出程序
        img = sensor.snapshot(chn = CAM_CHN_ID_0)#读取通道0的每一张图像

        # 改进的图像预处理
        # 1. 可选的ROI裁剪
        # img = img.copy(roi = roi)

        # 2. 改进的二值化处理
        img_binary = img.binary([(0, 36)])

        # 3. 形态学操作去噪（可选）
        if ENABLE_MORPHOLOGY:
            img_binary = img_binary.erode(1)   # 腐蚀去除小噪点
            img_binary = img_binary.dilate(1)  # 膨胀恢复目标大小

#----------------------------------------改进的矩形识别-----------------------------------------------#
        # 动态调整阈值，根据图像内容自适应
        base_threshold = 30000
        roi_area = (0, 40, 320, 160)

        # 尝试不同阈值找到合适的矩形
        for threshold_factor in [1.0, 0.7, 0.5, 1.5]:
            current_threshold = int(base_threshold * threshold_factor)
            rects = img_binary.find_rects(threshold=current_threshold, roi=roi_area)

            if rects:
                # 使用改进的筛选函数
                rect = filter_and_select_rect(rects)
                if rect:
                    break
        else:
            rect = None

        if rect:#排除找不到矩形后，乱画矩形
            # 绘制识别到的矩形和中心点
            img.draw_rectangle(rect.rect(), color = (255, 0, 0))
            local_x, local_y = (rect.x() +int(rect.w() / 2), rect.y() + int(rect.h() / 2))
            img.draw_cross((local_x, local_y) ,color = (255, 0, 0))

            # 显示矩形信息（调试用）
            w, h = rect.w(), rect.h()
            area = w * h
            aspect_ratio = max(w, h) / min(w, h)
            print(f"矩形信息: 位置({rect.x()},{rect.y()}) 尺寸({w}x{h}) 面积({area}) 长宽比({aspect_ratio:.2f})")

            # --- PI控制逻辑 ---
            # 1. 计算与屏幕中心的误差
            error_x = local_x - (WIDTH / 2)

            # 2. 应用中心死区
            if abs(error_x) > DEAD_ZONE:
                # 3. 积分项累积
                error_integral += error_x

                # 4. 积分限幅，防止积分饱和
                if error_integral > MAX_INTEGRAL:
                    error_integral = MAX_INTEGRAL
                elif error_integral < -MAX_INTEGRAL:
                    error_integral = -MAX_INTEGRAL

                # 5. PI控制器计算输出
                p_output = error_x * P_GAIN        # 比例项
                i_output = error_integral * I_GAIN  # 积分项
                pi_output = p_output + i_output     # PI控制器总输出

                # 6. 转换为步数
                steps_to_move = int(pi_output)

                # 7. 构建并发送指令
                # 限制最大步数，防止失控
                steps_to_move = max(-9999, min(9999, steps_to_move))

                sign = '+' if steps_to_move >= 0 else '-'
                command = "s%c%04db" % (sign, abs(steps_to_move))

                uart.send(command)
                print(f"Error: {error_x:.1f}, Integral: {error_integral:.1f}, P: {p_output:.1f}, I: {i_output:.1f}, Steps: {steps_to_move}")
            else:
                # 在死区内时，不累积积分项，避免积分漂移
                print(f"In dead zone, error: {error_x:.1f}")

        img = img.crop(x_scale = 320 / 160, y_scale = 240 / 120)
        img.draw_string_advanced(50, 50, 80, "fps: {}".format(fps.fps()), color = (255, 0, 0))#打印帧率
#        img.compressed_for_ide()#传输图片到ide
        Display.show_image(img)#显示在屏幕上
        gc.collect()#执行垃圾回收
#        time.sleep_ms(5)

#------------------------------------异常捕获+退出数据清理-------------------------------------------#

except KeyboardInterrupt as e:# 捕获键盘中断异常（用户手动停止）
    print("用户停止: ", e)
except BaseException as e:# 捕获所有其他异常
    print(f"异常: '{e}'")
finally:#无论如何都执行清理工作
    if 'sensor' in globals() and isinstance(sensor, Sensor):# 停止传感器运行（如果传感器对象存在）
        sensor.stop()
    # 反初始化显示模块，串口模块
    Display.deinit()
    uart.deinit()
    # 设置退出点，允许进入睡眠模式
    os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
    time.sleep_ms(100)
    # 释放媒体缓冲区
    MediaManager.deinit()
